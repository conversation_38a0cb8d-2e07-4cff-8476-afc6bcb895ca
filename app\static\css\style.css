/* Material Design 3 - 有聲圖書生成器 */

/* CSS 自定義屬性（變量） - 明暗主題支持 */
:root[data-theme="light"] {
  /* Primary Colors */
  --md-sys-color-primary: #6750a4;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #eaddff;
  --md-sys-color-on-primary-container: #21005d;

  /* Secondary Colors */
  --md-sys-color-secondary: #625b71;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #e8def8;
  --md-sys-color-on-secondary-container: #1d192b;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #7d5260;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #ffd8e4;
  --md-sys-color-on-tertiary-container: #31111d;

  /* Error Colors */
  --md-sys-color-error: #ba1a1a;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #ffdad6;
  --md-sys-color-on-error-container: #410002;

  /* Surface Colors */
  --md-sys-color-surface: #fffbfe;
  --md-sys-color-on-surface: #1c1b1f;
  --md-sys-color-surface-variant: #e7e0ec;
  --md-sys-color-on-surface-variant: #49454f;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f7f2fa;
  --md-sys-color-surface-container: #f3edf7;
  --md-sys-color-surface-container-high: #ece6f0;
  --md-sys-color-surface-container-highest: #e6e0e9;

  /* Outline */
  --md-sys-color-outline: #79747e;
  --md-sys-color-outline-variant: #cac4d0;

  /* Background */
  --md-sys-color-background: #fffbfe;
  --md-sys-color-on-background: #1c1b1f;

  /* Shadow */
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
}

:root[data-theme="dark"] {
  /* Primary Colors */
  --md-sys-color-primary: #d0bcff;
  --md-sys-color-on-primary: #381e72;
  --md-sys-color-primary-container: #4f378b;
  --md-sys-color-on-primary-container: #eaddff;

  /* Secondary Colors */
  --md-sys-color-secondary: #ccc2dc;
  --md-sys-color-on-secondary: #332d41;
  --md-sys-color-secondary-container: #4a4458;
  --md-sys-color-on-secondary-container: #e8def8;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #efb8c8;
  --md-sys-color-on-tertiary: #492532;
  --md-sys-color-tertiary-container: #633b48;
  --md-sys-color-on-tertiary-container: #ffd8e4;

  /* Error Colors */
  --md-sys-color-error: #ffb4ab;
  --md-sys-color-on-error: #690005;
  --md-sys-color-error-container: #93000a;
  --md-sys-color-on-error-container: #ffdad6;

  /* Surface Colors */
  --md-sys-color-surface: #141218;
  --md-sys-color-on-surface: #e6e0e9;
  --md-sys-color-surface-variant: #49454f;
  --md-sys-color-on-surface-variant: #cac4d0;
  --md-sys-color-surface-container-lowest: #0f0d13;
  --md-sys-color-surface-container-low: #1d1b20;
  --md-sys-color-surface-container: #211f26;
  --md-sys-color-surface-container-high: #2b2930;
  --md-sys-color-surface-container-highest: #36343b;

  /* Outline */
  --md-sys-color-outline: #938f99;
  --md-sys-color-outline-variant: #49454f;

  /* Background */
  --md-sys-color-background: #141218;
  --md-sys-color-on-background: #e6e0e9;

  /* Shadow */
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;
}

/* Typography Scale */
:root {
  --md-sys-typescale-display-large-font: "Roboto", sans-serif;
  --md-sys-typescale-display-large-size: 57px;
  --md-sys-typescale-display-large-line-height: 64px;
  --md-sys-typescale-display-large-weight: 400;

  --md-sys-typescale-headline-large-font: "Roboto", sans-serif;
  --md-sys-typescale-headline-large-size: 32px;
  --md-sys-typescale-headline-large-line-height: 40px;
  --md-sys-typescale-headline-large-weight: 400;

  --md-sys-typescale-title-large-font: "Roboto", sans-serif;
  --md-sys-typescale-title-large-size: 22px;
  --md-sys-typescale-title-large-line-height: 28px;
  --md-sys-typescale-title-large-weight: 400;

  --md-sys-typescale-body-large-font: "Roboto", sans-serif;
  --md-sys-typescale-body-large-size: 16px;
  --md-sys-typescale-body-large-line-height: 24px;
  --md-sys-typescale-body-large-weight: 400;

  --md-sys-typescale-label-large-font: "Roboto", sans-serif;
  --md-sys-typescale-label-large-size: 14px;
  --md-sys-typescale-label-large-line-height: 20px;
  --md-sys-typescale-label-large-weight: 500;

  /* Elevation */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

  /* Shape */
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
}

/* Base Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: var(--md-sys-typescale-body-large-font);
  font-size: var(--md-sys-typescale-body-large-size);
  line-height: var(--md-sys-typescale-body-large-line-height);
  color: var(--md-sys-color-on-background);
  background-color: var(--md-sys-color-background);
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  min-height: 100vh;
  font-weight: var(--md-sys-typescale-body-large-weight);
}

/* Material Components */

/* App Bar / Navigation */
.navbar {
  background-color: var(--md-sys-color-surface-container);
  color: var(--md-sys-color-on-surface);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--md-sys-elevation-level2);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  line-height: var(--md-sys-typescale-title-large-line-height);
  color: var(--md-sys-color-primary);
}

.nav-brand i {
  font-size: 24px;
}

.nav-menu {
  display: flex;
  gap: 8px;
  margin-left: auto;
  margin-right: 16px;
}

.nav-btn {
  background: transparent;
  border: none;
  color: var(--md-sys-color-on-surface-variant);
  padding: 12px 16px;
  border-radius: var(--md-sys-shape-corner-large);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--md-sys-color-on-surface);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.nav-btn:hover::before {
  opacity: 0.08;
}

.nav-btn:active::before {
  opacity: 0.12;
}

.nav-btn.active {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Navigation User Info */
.nav-user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info-mini {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
}

.user-info-mini .user-email {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--md-sys-color-on-surface);
}

.user-info-mini .points {
  color: var(--md-sys-color-primary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-info-mini .points-clickable {
  background: transparent;
  border: none;
  color: var(--md-sys-color-primary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--md-sys-shape-corner-small);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.user-info-mini .points-clickable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.user-info-mini .points-clickable:hover::before {
  opacity: 0.08;
}

.user-info-mini .points-clickable:active::before {
  opacity: 0.12;
}

.user-info-mini .points-clickable:hover {
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.refresh-points-btn {
  background: transparent;
  border: none;
  color: var(--md-sys-color-on-surface-variant);
  padding: 2px 4px;
  border-radius: var(--md-sys-shape-corner-extra-small);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  margin-left: 4px;
  opacity: 0.7;
}

.refresh-points-btn:hover {
  background: var(--md-sys-color-on-surface);
  opacity: 0.12;
  color: var(--md-sys-color-primary);
  transform: rotate(180deg);
}

.refresh-points-btn:active {
  transform: rotate(360deg);
}

.refresh-points-btn.spinning {
  animation: spin 1s linear infinite;
}

.user-actions-mini {
  display: flex;
  gap: 8px;
}

.user-actions-mini .md-button {
  padding: 8px 12px;
  font-size: 12px;
  white-space: nowrap;
}

.user-actions-mini .btn-text {
  display: inline;
}

/* Material Design 3 Buttons */
.md-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 24px;
  border-radius: var(--md-sys-shape-corner-large);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: var(--md-sys-typescale-label-large-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
}

.md-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.md-button:hover::before {
  opacity: 0.08;
}

.md-button:active::before {
  opacity: 0.12;
}

.md-button:disabled {
  opacity: 0.38;
  cursor: not-allowed;
}

/* Button Variants */
.md-filled-button {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-level0);
}

.md-filled-button:hover {
  box-shadow: var(--md-sys-elevation-level1);
}

.md-outlined-button {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 1px solid var(--md-sys-color-outline);
}

.md-text-button {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  padding: 10px 12px;
}

.md-tonal-button {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

/* Main Content */
main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* Cards */
.md-card {
  background-color: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.md-card:hover {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-card-elevated {
  background-color: var(--md-sys-color-surface-container-low);
  box-shadow: var(--md-sys-elevation-level1);
}

.md-card-filled {
  background-color: var(--md-sys-color-surface-container-highest);
  box-shadow: var(--md-sys-elevation-level0);
}

.md-card-outlined {
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline-variant);
  box-shadow: var(--md-sys-elevation-level0);
}

.md-card-content {
  padding: 16px;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.32);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--md-sys-color-surface-container-high);
  padding: 24px;
  border-radius: var(--md-sys-shape-corner-extra-large);
  width: 90%;
  max-width: 400px;
  box-shadow: var(--md-sys-elevation-level3);
  position: relative;
}

.close {
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 24px;
  cursor: pointer;
  color: var(--md-sys-color-on-surface-variant);
  background: none;
  border: none;
  padding: 8px;
  border-radius: var(--md-sys-shape-corner-small);
  transition: all 0.2s ease;
}

.close:hover {
  background-color: var(--md-sys-color-on-surface);
  opacity: 0.08;
}

/* Forms */
form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.md-text-field {
  position: relative;
  margin-bottom: 8px;
}

.md-text-field input {
  width: 100%;
  padding: 16px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-body-large-size);
  transition: all 0.2s ease;
}

.md-text-field input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  border-width: 2px;
}

.md-text-field input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
}

/* Upload Section */
.upload-section {
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--md-sys-elevation-level1);
}

.upload-section h2 {
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  line-height: var(--md-sys-typescale-headline-large-line-height);
  color: var(--md-sys-color-on-surface);
  margin-bottom: 24px;
}

.upload-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background-color: var(--md-sys-color-surface-container-highest);
  border-radius: var(--md-sys-shape-corner-small);
  padding: 4px;
}

.tab-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-small);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tab-btn.active {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-level1);
}

.upload-zone {
  border: 2px dashed var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 48px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--md-sys-color-surface);
}

.upload-zone:hover {
  border-color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
}

.upload-zone i {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: 16px;
}

.upload-zone p {
  margin-bottom: 8px;
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface);
}

.upload-hint {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 14px;
}

.cost-info {
  margin-top: 16px;
  padding: 16px;
  background: var(--md-sys-color-primary-container);
  border-radius: var(--md-sys-shape-corner-medium);
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--md-sys-color-on-primary-container);
}

/* Tasks Section */
.tasks-section {
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 24px;
  box-shadow: var(--md-sys-elevation-level1);
}

.tasks-section h2 {
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  color: var(--md-sys-color-on-surface);
  margin-bottom: 24px;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-item {
  background: var(--md-sys-color-surface-container-highest);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.task-item:hover {
  background: var(--md-sys-color-surface-container-high);
  box-shadow: var(--md-sys-elevation-level1);
}

.task-info h4 {
  margin-bottom: 8px;
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: 500;
}

.task-info p {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 14px;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.status-pending {
  color: var(--md-sys-color-primary);
}

.status-success {
  color: var(--md-sys-color-tertiary);
}

.status-failed {
  color: var(--md-sys-color-error);
}

/* Theme Toggle */
.theme-toggle {
  background: transparent;
  border: none;
  color: var(--md-sys-color-on-surface-variant);
  padding: 12px;
  border-radius: var(--md-sys-shape-corner-large);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 20px;
}

.theme-toggle:hover {
  background: var(--md-sys-color-on-surface);
  opacity: 0.08;
}

/* Message/Toast */
.message {
  position: fixed;
  top: 100px;
  right: 24px;
  padding: 16px 24px;
  border-radius: var(--md-sys-shape-corner-large);
  color: var(--md-sys-color-on-primary);
  font-weight: 500;
  z-index: 1001;
  transform: translateX(400px);
  transition: transform 0.3s ease;
  max-width: 320px;
  box-shadow: var(--md-sys-elevation-level3);
}

.message.show {
  transform: translateX(0);
}

.message.success {
  background: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

.message.error {
  background: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

.message.info {
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

/* Page Content */
.page-content {
  animation: fadeIn 0.3s ease-out;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.page-header h1 {
  color: var(--md-sys-color-on-surface);
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.page-header p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0.25rem 0 0 0;
  font-size: 1rem;
}

.page-header .header-content {
  flex: 1;
}

#refresh-tasks-btn {
  flex-shrink: 0;
  margin-left: auto;
  white-space: nowrap;
}

#refresh-tasks-btn:hover {
  background: var(--md-sys-color-primary-container);
}

#refresh-tasks-btn.refreshing {
  pointer-events: none;
  opacity: 0.7;
}

#refresh-tasks-btn.refreshing i {
  animation: spin 1s linear infinite;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 1rem;
  }
  
  .page-header .header-content {
    order: 1;
  }
  
  #refresh-tasks-btn {
    order: 2;
    margin-left: 0;
    align-self: center;
  }
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);
  overflow: hidden;
  margin-top: 8px;
}

.progress-fill {
  height: 100%;
  background: var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-extra-small);
  transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    padding: 12px 16px;
    flex-wrap: wrap;
    gap: 8px;
    min-height: auto;
  }

  .nav-brand {
    order: 1;
    flex: 1;
    min-width: 0;
  }

  .nav-brand span {
    font-size: 18px;
  }

  .theme-toggle {
    order: 2;
    flex-shrink: 0;
  }

  .nav-user,
  .nav-user-info {
    order: 3;
    flex-shrink: 0;
  }

  .nav-menu {
    order: 4;
    width: 100%;
    margin: 12px 0 0 0;
    justify-content: space-around;
    background: var(--md-sys-color-surface-container-high);
    border-radius: var(--md-sys-shape-corner-medium);
    padding: 8px;
    gap: 4px;
  }

  .nav-btn {
    flex: 1;
    padding: 8px 4px;
    font-size: 12px;
    min-width: 0;
    justify-content: center;
  }

  .nav-btn i {
    font-size: 16px;
    margin-right: 0;
    margin-bottom: 2px;
  }

  .nav-user-info {
    background: var(--md-sys-color-surface-container-high);
    border-radius: var(--md-sys-shape-corner-medium);
    padding: 8px;
    gap: 8px;
    align-items: center;
  }

  .user-info-mini {
    text-align: center;
    font-size: 12px;
  }

  .user-info-mini .user-email {
    font-size: 12px;
    margin-bottom: 2px;
  }

  .user-info-mini .points {
    font-size: 11px;
  }

  .user-actions-mini {
    gap: 4px;
  }

  .user-actions-mini .btn-text {
    display: none;
  }

  .user-actions-mini .md-button {
    padding: 6px;
    min-width: 36px;
    font-size: 14px;
    border-radius: var(--md-sys-shape-corner-small);
  }

  main {
    padding: 16px;
  }

  .hero h1 {
    font-size: 32px;
  }

  .hero-desc {
    font-size: 16px;
  }

  .features {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .feature {
    padding: 24px 20px;
  }

  .upload-zone {
    padding: 32px 16px;
  }

  .upload-zone i {
    font-size: 40px;
  }

  .upload-tabs {
    margin-bottom: 16px;
  }

  .tab-btn {
    padding: 10px 12px;
    font-size: 14px;
  }

  .audio-library-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .player-header .md-card-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .player-options {
    flex-direction: column;
    gap: 16px;
  }
  
  .control-btn {
    width: 48px;
    height: 48px;
  }
  
  .control-btn.play-btn {
    width: 64px;
    height: 64px;
  }
  
  .bookmark-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .bookmark-item .bookmark-actions {
    align-self: stretch;
    justify-content: flex-end;
  }

  .task-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .task-meta {
    flex-direction: column;
    gap: 8px;
  }

  .task-actions {
    flex-direction: column;
  }

  .task-actions .md-button {
    width: 100%;
  }

  .audio-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .audio-controls audio {
    max-width: 100%;
  }

  .points-stats {
    grid-template-columns: 1fr;
  }

  .points-record {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .record-amount {
    text-align: center;
  }

  .player-buttons {
    gap: 12px;
  }

  .volume-control input[type="range"] {
    width: 80px;
  }

  .modal-content {
    width: 90%;
    max-width: none;
    margin: 20px;
  }

  .library-filters,
  .task-filters {
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }

  .filter-btn,
  .library-filter-btn {
    flex: 0 0 auto;
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* 超小屏幕优化 (小于480px) */
@media (max-width: 480px) {
  .navbar {
    padding: 8px 12px;
  }

  .nav-brand {
    font-size: 16px;
  }

  .nav-brand i {
    font-size: 20px;
  }

  .nav-brand span {
    font-size: 16px;
  }

  .theme-toggle {
    padding: 8px;
    font-size: 18px;
  }

  .nav-btn {
    padding: 6px 2px;
    font-size: 10px;
    flex-direction: column;
  }

  .nav-btn i {
    font-size: 14px;
    margin-bottom: 2px;
  }

  .user-info-mini {
    font-size: 11px;
  }

  .user-info-mini .user-email {
    font-size: 11px;
    margin-bottom: 1px;
  }

  .user-info-mini .points {
    font-size: 10px;
  }

  .refresh-points-btn {
    font-size: 9px;
    padding: 1px;
    margin-left: 1px;
  }

  .user-actions-mini .md-button {
    padding: 4px;
    min-width: 32px;
    font-size: 12px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .page-header p {
    font-size: 14px;
  }

  .hero h1 {
    font-size: 28px;
    line-height: 1.2;
  }

  .hero-desc {
    font-size: 14px;
  }

  .feature {
    padding: 20px 16px;
  }

  .feature h3 {
    font-size: 18px;
  }

  .feature p {
    font-size: 14px;
  }

  .upload-zone {
    padding: 24px 12px;
  }

  .upload-zone i {
    font-size: 36px;
  }

  .upload-zone p {
    font-size: 14px;
  }

  .upload-hint {
    font-size: 12px;
  }

  .modal-content {
    width: 95%;
    padding: 16px;
  }

  .md-button {
    padding: 8px 16px;
    font-size: 14px;
  }

  .btn-large {
    padding: 12px 24px;
    font-size: 16px;
  }

  .audio-book-item {
    padding: 16px;
  }

  .audio-book-item .book-title {
    font-size: 16px;
  }

  .my-task-item {
    padding: 16px;
  }

  .task-title h3 {
    font-size: 16px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-number {
    font-size: 24px;
  }
}

/* 横屏手机优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .nav-menu {
    margin: 8px 0 0 0;
  }

  .nav-btn {
    padding: 6px 8px;
    font-size: 11px;
  }

  .user-info-mini {
    font-size: 11px;
  }

  .hero {
    padding: 32px 24px;
  }

  .hero h1 {
    font-size: 28px;
  }

  .features {
    grid-template-columns: repeat(3, 1fr);
  }

  .feature {
    padding: 16px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* 兼容舊類名 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 24px;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.btn:hover::before {
  opacity: 0.08;
}

.btn:active::before {
  opacity: 0.12;
}

.btn:disabled {
  opacity: 0.38;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 24px;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-level1);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.btn-primary:hover {
  box-shadow: var(--md-sys-elevation-level2);
  background-color: var(--md-sys-color-primary);
}

.btn-primary:hover::before {
  opacity: 0.08;
}

.btn-primary:active::before {
  opacity: 0.12;
}

.btn-primary:disabled {
  opacity: 0.38;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 24px;
  border: none;
  border-radius: var(--md-sys-shape-corner-full);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.btn-secondary:hover::before {
  opacity: 0.08;
}

.btn-secondary:active::before {
  opacity: 0.12;
}

.btn-secondary:disabled {
  opacity: 0.38;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 24px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-full);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.1px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.btn-outline:hover::before {
  opacity: 0.08;
}

.btn-outline:active::before {
  opacity: 0.12;
}

.btn-outline:disabled {
  opacity: 0.38;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-large {
  padding: 14px 32px;
  font-size: 16px;
}

/* 音频库和播放器样式將繼承上述Material Design样式 */
.audio-library-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.audio-book-item {
  background-color: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 16px;
  box-shadow: var(--md-sys-elevation-level1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.audio-book-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--md-sys-elevation-level3);
}



/* 音频库页面样式 */
/* 页面头部布局 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  gap: 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 书籍数量显示（右上角） */
.library-book-count-header {
  background: var(--md-sys-color-surface-container-highest);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  padding: 8px 16px;
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
}

.book-count-text {
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.warning-text {
  color: var(--md-sys-color-error);
  margin-left: 12px;
  font-size: 12px;
}

/* 标签式排序控件 */
.library-sort-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 32px;
  background: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 4px;
  box-shadow: var(--md-sys-elevation-level1);
}

.sort-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  position: relative;
}

.sort-tab:hover {
  background: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface);
}

.sort-tab.active {
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-level2);
}

.sort-tab i {
  font-size: 16px;
}

/* 旧的过滤器样式（保留兼容性） */
.library-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  justify-content: center;
}

.library-filter-btn {
  transition: all 0.2s ease;
}

.library-filter-btn.active {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.audio-library-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.audio-book-item {
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 20px;
  box-shadow: var(--md-sys-elevation-level1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.audio-book-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--md-sys-elevation-level3);
}

.audio-book-item .book-cover {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--md-sys-color-primary), var(--md-sys-color-tertiary));
  border-radius: var(--md-sys-shape-corner-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.audio-book-item .book-cover i {
  font-size: 28px;
  color: var(--md-sys-color-on-primary);
}

.audio-book-item .book-title {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--md-sys-color-on-surface);
  line-height: 1.4;
}

.audio-book-item .book-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 16px;
}

.audio-book-item .book-progress {
  margin-bottom: 16px;
}

.audio-book-item .progress-label {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 8px;
}

.audio-book-item .progress-bar-mini {
  height: 4px;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);
  overflow: hidden;
}

.audio-book-item .progress-fill-mini {
  height: 100%;
  background: var(--md-sys-color-primary);
  transition: width 0.3s ease;
  border-radius: var(--md-sys-shape-corner-extra-small);
}

.audio-book-item .book-actions {
  display: flex;
  gap: 8px;
}

.audio-book-item .btn-play {
  flex: 1;
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border: none;
  padding: 12px;
  border-radius: var(--md-sys-shape-corner-medium);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--md-sys-elevation-level0);
}

.audio-book-item .btn-play:hover {
  box-shadow: var(--md-sys-elevation-level1);
  transform: translateY(-2px);
}

.audio-book-item .btn-options {
  background: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface-variant);
  border: 1px solid var(--md-sys-color-outline-variant);
  padding: 12px 16px;
  border-radius: var(--md-sys-shape-corner-medium);
  cursor: pointer;
  transition: all 0.3s ease;
}

.audio-book-item .btn-options:hover {
  background: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
  border-color: var(--md-sys-color-secondary);
}

/* 分页导航样式 */
.library-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  margin-top: 48px;
  padding: 24px;
  background: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level1);
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 1px solid var(--md-sys-color-outline);
  background: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  border-radius: var(--md-sys-shape-corner-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  min-width: 100px;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border-color: var(--md-sys-color-primary);
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
}

.pagination-btn:disabled {
  opacity: 0.38;
  cursor: not-allowed;
  background: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  border-color: var(--md-sys-color-outline-variant);
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
  background: var(--md-sys-color-surface-container-highest);
  padding: 12px 20px;
  border-radius: var(--md-sys-shape-corner-medium);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.pagination-info span {
  font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-right {
    justify-content: center;
  }

  .library-sort-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }

  .sort-tab {
    flex: 1;
    min-width: 120px;
  }

  .library-pagination {
    flex-direction: column;
    gap: 16px;
  }

  .pagination-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* 音频播放器页面样式 */
.player-header {
  margin-bottom: 24px;
}

.player-header .md-card-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.player-header .book-info {
  flex: 1;
}

.player-header .book-info h1 {
  margin: 0 0 8px 0;
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  color: var(--md-sys-color-on-surface);
}

.player-header .book-info p {
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
  font-size: 14px;
}

.audio-player-container {
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 32px 24px;
  margin-bottom: 24px;
  box-shadow: var(--md-sys-elevation-level1);
}

.player-controls {
  text-align: center;
}

.progress-container {
  margin-bottom: 32px;
}

.time-display {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  font-family: 'Courier New', monospace;
}

.progress-bar-container {
  position: relative;
  height: 6px;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);
  overflow: hidden;
  cursor: pointer;
}

.progress-bar-track {
  position: relative;
  height: 100%;
}

.progress-bar-fill {
  height: 100%;
  background: var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-extra-small);
  transition: width 0.1s ease;
}

.progress-handle {
  position: absolute;
  top: 50%;
  width: 16px;
  height: 16px;
  background: var(--md-sys-color-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  transition: all 0.2s ease;
  opacity: 0;
  box-shadow: var(--md-sys-elevation-level2);
}

.progress-bar-container:hover .progress-handle {
  opacity: 1;
}

.progress-handle:active {
  cursor: grabbing;
  transform: translate(-50%, -50%) scale(1.2);
}

.player-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.control-btn {
  background: var(--md-sys-color-surface-container-highest);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--md-sys-color-on-surface);
  box-shadow: var(--md-sys-elevation-level1);
}

.control-btn:hover {
  background: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
  border-color: var(--md-sys-color-secondary);
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
}

.control-btn.play-btn {
  width: 72px;
  height: 72px;
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border-color: var(--md-sys-color-primary);
}

.control-btn.play-btn:hover {
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--md-sys-elevation-level3);
}

.player-options {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  flex-wrap: wrap;
}

.speed-control,
.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.speed-control label,
.volume-control label {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  font-weight: 500;
}

.speed-control select {
  padding: 8px 12px;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: var(--md-sys-shape-corner-small);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.speed-control select:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
}

.volume-control i {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 18px;
}

.volume-control input[type="range"] {
  width: 100px;
  height: 6px;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small);
  outline: none;
  cursor: pointer;
  appearance: none;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--md-sys-color-primary);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--md-sys-elevation-level1);
}

.volume-control input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--md-sys-color-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: var(--md-sys-elevation-level1);
}

#audio-element {
  display: none;
}

.playback-info {
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level1);
}

.bookmark-section h3 {
  margin-bottom: 16px;
  color: var(--md-sys-color-on-surface);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: 500;
}

.bookmark-section h3 i {
  color: var(--md-sys-color-primary);
}

.bookmarks-list {
  margin-top: 16px;
}

.bookmark-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--md-sys-color-surface-container-highest);
  border-radius: var(--md-sys-shape-corner-medium);
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.bookmark-item:hover {
  background: var(--md-sys-color-secondary-container);
  transform: translateX(4px);
}

.bookmark-item .bookmark-info {
  flex: 1;
}

.bookmark-item .bookmark-time {
  font-family: 'Courier New', monospace;
  color: var(--md-sys-color-primary);
  font-weight: 500;
  font-size: 14px;
}

.bookmark-item .bookmark-note {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  margin-top: 4px;
}

.bookmark-item .bookmark-actions {
  display: flex;
  gap: 8px;
}

.bookmark-item .btn-small {
  padding: 8px 12px;
  font-size: 12px;
  border-radius: var(--md-sys-shape-corner-small);
}

/* 我的任务页面样式 */
.task-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-btn {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  border: 1px solid var(--md-sys-color-outline-variant);
  border-radius: var(--md-sys-shape-corner-small);
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.filter-btn:hover {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

.filter-btn.active {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
  border-color: var(--md-sys-color-secondary);
}

.my-tasks-list {
  max-width: 1000px;
  margin: 0 auto;
}

.my-task-item {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: var(--md-sys-elevation-level1);
  transition: all 0.3s ease;
  border-left: 4px solid var(--md-sys-color-outline-variant);
}

.my-task-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
}

.my-task-item.status-completed {
  border-left-color: var(--md-sys-color-tertiary);
}

.my-task-item.status-pending {
  border-left-color: var(--md-sys-color-primary);
}

.my-task-item.status-failed {
  border-left-color: var(--md-sys-color-error);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.task-title {
  flex: 1;
}

.task-title h3 {
  margin: 0 0 8px 0;
  color: var(--md-sys-color-on-surface);
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: 500;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.task-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-status-badge {
  padding: 6px 12px;
  border-radius: var(--md-sys-shape-corner-large);
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.task-status-badge.pending {
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
}

.task-status-badge.completed {
  background: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
}

.task-status-badge.failed {
  background: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
}

.task-progress {
  margin-top: 1rem;
  opacity: 1;
  transition: opacity 0.3s ease;
  transform: translateZ(0); /* 硬件加速 */
  backface-visibility: hidden; /* 防止闪烁 */
}

.task-progress .progress-bar {
  background: var(--md-sys-color-surface-variant);
  border-radius: 8px;
  overflow: hidden;
  height: 8px;
  position: relative;
  margin-bottom: 0.5rem;
}

.task-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--md-sys-color-primary), var(--md-sys-color-primary-container));
  border-radius: 8px;
  width: 0%;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 更平滑的动画 */
  transform: translateZ(0); /* 硬件加速 */
  will-change: width; /* 提示浏览器优化 */
}

.task-progress .progress-text {
  text-align: center;
  margin: 0.5rem 0 0 0;
  color: var(--md-sys-color-on-surface-variant);
  font-size: 0.9rem;
  font-weight: 500;
  transform: translateZ(0); /* 硬件加速 */
  backface-visibility: hidden; /* 防止字体闪烁 */
}

.task-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.task-actions .md-button {
  font-size: 14px;
  padding: 8px 16px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  background: var(--md-sys-color-surface-container-highest);
  padding: 16px;
  border-radius: var(--md-sys-shape-corner-medium);
  margin-top: 16px;
}

.audio-controls audio {
  flex: 1;
  max-width: 300px;
}

.download-info {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 积分统计页面样式 */
.points-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  background: var(--md-sys-color-surface-container);
  padding: 24px;
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level1);
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--md-sys-elevation-level2);
}

.stat-card i {
  font-size: 32px;
  margin-bottom: 12px;
}

.stat-card.current-balance i {
  color: var(--md-sys-color-primary);
}

.stat-card.total-earned i {
  color: var(--md-sys-color-tertiary);
}

.stat-card.total-consumed i {
  color: var(--md-sys-color-error);
}

.stat-card.total-records i {
  color: var(--md-sys-color-secondary);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--md-sys-color-on-surface);
}

.stat-label {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 14px;
  font-weight: 500;
}

/* 积分记录 */
.points-history {
  max-width: 800px;
  margin: 0 auto;
}

.points-record {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 16px 20px;
  margin-bottom: 8px;
  box-shadow: var(--md-sys-elevation-level1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.points-record:hover {
  transform: translateX(8px);
  box-shadow: var(--md-sys-elevation-level2);
}

.record-info {
  flex: 1;
}

.record-info h4 {
  margin: 0 0 4px 0;
  color: var(--md-sys-color-on-surface);
  font-size: 16px;
  font-weight: 500;
}

.record-info p {
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
  font-size: 14px;
}

.record-amount {
  text-align: right;
}

.amount-change {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.amount-change.positive {
  color: var(--md-sys-color-tertiary);
}

.amount-change.negative {
  color: var(--md-sys-color-error);
}

.balance-after {
  font-size: 12px;
  color: var(--md-sys-color-on-surface-variant);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 48px;
  color: var(--md-sys-color-on-surface-variant);
  font-size: 16px;
}

.loading i {
  margin-right: 8px;
  color: var(--md-sys-color-primary);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px;
  color: var(--md-sys-color-on-surface-variant);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--md-sys-color-outline);
}

.empty-state h3 {
  margin-bottom: 8px;
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-title-large-size);
}

.empty-state p {
  margin-bottom: 24px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 新增：Landing 首頁樣式 */

/* Landing Section */
.landing-section {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 英雄區域 */
.hero-landing {
  display: flex;
  align-items: center;
  min-height: 70vh;
  gap: 60px;
  margin-bottom: 100px;
  padding: 60px 0;
}

.hero-content {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: var(--md-sys-typescale-display-large-size);
  font-weight: var(--md-sys-typescale-display-large-weight);
  line-height: var(--md-sys-typescale-display-large-line-height);
  color: var(--md-sys-color-primary);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.hero-title i {
  font-size: 48px;
  animation: float 3s ease-in-out infinite;
}

.hero-subtitle {
  font-size: var(--md-sys-typescale-headline-large-size);
  line-height: var(--md-sys-typescale-headline-large-line-height);
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 40px;
}

.hero-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 48px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36px;
  font-weight: 700;
  color: var(--md-sys-color-primary);
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  margin-top: 4px;
}

.hero-illustration {
  flex: 0 0 300px;
  height: 300px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-elements {
  display: flex;
  align-items: center;
  gap: 40px;
  animation: float 4s ease-in-out infinite;
}

.book-icon, .audio-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--md-sys-shape-corner-large);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  font-size: 36px;
  box-shadow: var(--md-sys-elevation-level3);
}

.arrow-icon {
  font-size: 24px;
  color: var(--md-sys-color-primary);
  animation: slide 2s ease-in-out infinite;
}

/* 示例音頻展示區域 */
.demo-section, .how-to-use-section, .features-section {
  margin-bottom: 100px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  color: var(--md-sys-color-on-surface);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.section-header h2 i {
  color: var(--md-sys-color-primary);
}

.section-header p {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  max-width: 600px;
  margin: 0 auto;
}

.demo-books {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.demo-book-item {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 24px;
  display: flex;
  gap: 20px;
  box-shadow: var(--md-sys-elevation-level1);
  transition: all 0.3s ease;
}

.demo-book-item:hover {
  box-shadow: var(--md-sys-elevation-level3);
  transform: translateY(-4px);
}

.book-cover {
  width: 80px;
  height: 120px;
  border-radius: var(--md-sys-shape-corner-small);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 32px;
  color: white;
  flex-shrink: 0;
}

.epub-book {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.web-book {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.pdf-book {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.format-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: var(--md-sys-shape-corner-extra-small);
  font-weight: 500;
}

.book-info {
  flex: 1;
}

.book-info h4 {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  color: var(--md-sys-color-on-surface);
  margin-bottom: 8px;
}

.book-meta {
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 8px;
}

.book-desc {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  margin-bottom: 16px;
  line-height: 1.5;
}

.demo-audio-controls audio {
  width: 100%;
  height: 40px;
}

/* 使用說明區域 */
.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.step-item {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin: 0 auto 24px;
  box-shadow: var(--md-sys-elevation-level2);
}

.step-content h3 {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  color: var(--md-sys-color-on-surface);
  margin-bottom: 16px;
}

.step-content p {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.6;
  margin-bottom: 20px;
}

.step-formats, .conversion-features, .playback-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.format-tag, .feature-tag {
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  padding: 6px 12px;
  border-radius: var(--md-sys-shape-corner-small);
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 特色功能區域 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.feature-card {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: 32px 24px;
  text-align: center;
  box-shadow: var(--md-sys-elevation-level1);
  transition: all 0.3s ease;
}

.feature-card:hover {
  box-shadow: var(--md-sys-elevation-level3);
  transform: translateY(-4px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 32px;
  box-shadow: var(--md-sys-elevation-level2);
}

.feature-card h3 {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  color: var(--md-sys-color-on-surface);
  margin-bottom: 16px;
}

.feature-card p {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-surface-variant);
  line-height: 1.6;
}

/* CTA 區域 */
.cta-section {
  background: var(--md-sys-color-primary-container);
  border-radius: var(--md-sys-shape-corner-extra-large);
  padding: 60px 40px;
  text-align: center;
  margin-bottom: 60px;
}

.cta-content h2 {
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  color: var(--md-sys-color-on-primary-container);
  margin-bottom: 16px;
}

.cta-content p {
  font-size: var(--md-sys-typescale-body-large-size);
  color: var(--md-sys-color-on-primary-container);
  margin-bottom: 32px;
  opacity: 0.8;
}

.cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 動畫效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slide {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(10px);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .hero-landing {
    flex-direction: column;
    text-align: center;
    min-height: auto;
    gap: 40px;
    margin-bottom: 60px;
    padding: 40px 0;
  }

  .hero-title {
    font-size: 36px;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .hero-stats {
    justify-content: center;
    gap: 24px;
  }

  .stat-number {
    font-size: 28px;
  }

  .hero-illustration {
    flex: none;
    width: 250px;
    height: 200px;
  }

  .floating-elements {
    gap: 20px;
  }

  .book-icon, .audio-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .demo-books {
    grid-template-columns: 1fr;
  }

  .demo-book-item {
    flex-direction: column;
    text-align: center;
  }

  .book-cover {
    width: 60px;
    height: 90px;
    margin: 0 auto;
    font-size: 24px;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .cta-section {
    padding: 40px 24px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .demo-section, .how-to-use-section, .features-section {
    margin-bottom: 60px;
  }
}

@media (max-width: 480px) {
  .landing-section {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 28px;
    flex-direction: column;
    gap: 8px;
  }

  .hero-title i {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .stat-number {
    font-size: 24px;
  }

  .section-header h2 {
    font-size: 24px;
    flex-direction: column;
    gap: 8px;
  }

  .step-number {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .format-tag, .feature-tag {
    font-size: 11px;
    padding: 4px 8px;
  }
}

.speed-control,
.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.speed-control,
.volume-control,
.force-seek-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.force-seek-control .md-button {
  font-size: 12px;
  padding: 8px 12px;
  border-radius: var(--md-sys-shape-corner-small);
  background-color: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
  border: 1px solid var(--md-sys-color-outline-variant);
  transition: all 0.2s ease;
}

.force-seek-control .md-button:hover {
  background-color: var(--md-sys-color-tertiary);
  color: var(--md-sys-color-on-tertiary);
  transform: translateY(-1px);
  box-shadow: var(--md-sys-elevation-level2);
}

.force-seek-control .md-button i {
  font-size: 14px;
  margin-right: 4px;
}

/* 处理选项样式 */
.processing-options {
    margin-top: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.processing-options h3 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option-item {
    position: relative;
}

.option-item input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.option-item label {
    display: block;
    padding: 16px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.option-item label:hover {
    border-color: #adb5bd;
    background: #f8f9fa;
}

.option-item input[type="radio"]:checked + label {
    border-color: #2196f3;
    background: #e3f2fd;
}

.option-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.option-header i {
    font-size: 1.2rem;
    color: #6c757d;
}

.option-item input[type="radio"]:checked + label .option-header i {
    color: #2196f3;
}

.option-title {
    font-weight: 600;
    color: #212529;
    font-size: 1rem;
}

.option-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: auto;
}

.option-badge {
    background: #28a745;
    color: white;
}

.option-badge.new {
    background: #ff6b35;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.option-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 增强选项详细设置 */
.enhanced-options {
    margin-top: 16px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.enhanced-option-group {
    margin-bottom: 20px;
}

.enhanced-option-group h4 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.strategy-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.strategy-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 0;
}

.strategy-item i {
    color: #2196f3;
    margin-top: 2px;
    flex-shrink: 0;
}

.strategy-content {
    font-size: 0.9rem;
    line-height: 1.4;
}

.strategy-content strong {
    color: #495057;
}

.enhanced-features h4 {
    margin-bottom: 12px;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #495057;
}

.feature-item i {
    color: #28a745;
    font-size: 0.8rem;
}

/* 增强版播放器页面样式 */
.player-mode-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mode-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.mode-badge.enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.enhanced-player-container {
    padding: 0;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .option-group {
        gap: 8px;
    }
    
    .option-item label {
        padding: 12px;
    }
    
    .option-header {
        gap: 8px;
    }
    
    .option-title {
        font-size: 0.9rem;
    }
    
    .strategy-info {
        gap: 6px;
    }
    
    .strategy-item {
        gap: 8px;
        padding: 6px 0;
    }
    
    .enhanced-options {
        padding: 12px;
    }
}

/* 深色模式支持 */
[data-theme="dark"] .processing-options {
    background: #2d2d2d;
    border-color: #404040;
}

[data-theme="dark"] .processing-options h3 {
    color: #ffffff;
}

[data-theme="dark"] .option-item label {
    background: #1a1a1a;
    border-color: #404040;
    color: #ffffff;
}

[data-theme="dark"] .option-item label:hover {
    border-color: #555555;
    background: #2d2d2d;
}

[data-theme="dark"] .option-item input[type="radio"]:checked + label {
    background: #1e3a5f;
}

[data-theme="dark"] .option-title {
    color: #ffffff;
}

[data-theme="dark"] .option-description {
    color: #cccccc;
}

[data-theme="dark"] .enhanced-options {
    background: #1a1a1a;
    border-color: #404040;
}

[data-theme="dark"] .enhanced-option-group h4 {
    color: #ffffff;
}

[data-theme="dark"] .strategy-content {
    color: #cccccc;
}

[data-theme="dark"] .strategy-content strong {
    color: #ffffff;
}

[data-theme="dark"] .feature-item {
    color: #cccccc;
}

/* 增强版音频书样式 */
.mode-badge.enhanced {
    position: absolute;
    top: 5px;
    right: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 2;
}

.chapter-count {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.85em;
}

.chapter-count::before {
    content: "📚 ";
    margin-right: 2px;
}

.audio-book-item .book-cover {
    position: relative;
    overflow: visible;
}

.audio-book-item.enhanced {
    border-left: 3px solid var(--primary-color);
}

.audio-book-item.enhanced .book-cover {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.audio-book-item.enhanced .book-cover i {
    color: white;
}

/* 增强版播放器页面样式 */
#enhanced-audio-player {
    padding: 0;
    background: var(--surface-color);
    min-height: 100vh;
}

#enhanced-player-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .mode-badge.enhanced {
        font-size: 8px;
        padding: 1px 4px;
        top: 3px;
        right: 3px;
    }
    
    .chapter-count {
        font-size: 0.8em;
    }
}

/* 暗色主题适配 */
[data-theme="dark"] .mode-badge.enhanced {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 2px 4px rgba(0,0,0,0.4);
}

[data-theme="dark"] .chapter-count {
    color: var(--accent-color);
}

/* 音频库统计信息样式 */
.library-stats {
  display: flex;
  gap: 16px;
  padding: 16px 20px;
  background-color: var(--md-sys-color-surface-container-low);
  border-radius: var(--md-sys-shape-corner-medium);
  margin-bottom: 24px;
  flex-wrap: wrap;
  justify-content: center;
}

.library-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--md-sys-color-on-surface-variant);
  padding: 4px 12px;
  background-color: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-small);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.library-stats .stat-item.warning {
  background-color: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
  border-color: var(--md-sys-color-error);
}

/* 修复任务提示样式 */
.fix-tasks-hint {
  background-color: var(--md-sys-color-primary-container);
  border: 1px solid var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-medium);
  margin-bottom: 24px;
  box-shadow: var(--md-sys-elevation-level1);
}

.fix-tasks-hint .hint-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  color: var(--md-sys-color-on-primary-container);
}

.fix-tasks-hint i {
  font-size: 18px;
  color: var(--md-sys-color-primary);
}

.fix-tasks-hint a {
  color: var(--md-sys-color-primary);
  text-decoration: underline;
  font-weight: 500;
}

.fix-tasks-hint a:hover {
  text-decoration: none;
}

.fix-tasks-hint .hint-close {
  background: none;
  border: none;
  color: var(--md-sys-color-on-primary-container);
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: var(--md-sys-shape-corner-extra-small);
  margin-left: auto;
  transition: background-color 0.2s ease;
}

.fix-tasks-hint .hint-close:hover {
  background-color: var(--md-sys-color-primary-container-dark);
}

.audio-library-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

/* 任务完成动画效果 */
@keyframes taskCompleted {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.01);
  }
  100% {
    transform: scale(1);
  }
}

/* 页面渲染优化，减少闪烁 */
.task-item, .my-task-item {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
  transition: transform 0.3s ease; /* 平滑的变换动画 */
}

/* 状态更新时的平滑过渡 */
.task-status, .task-status-badge {
  transition: all 0.3s ease;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 防止任务项布局闪烁 */
.my-tasks-list, .workshop-tasks-list {
  min-height: 200px;
  transform: translateZ(0); /* 硬件加速 */
}

/* 任务项容器优化 */
.my-task-item {
  contain: layout style; /* 优化渲染性能 */
  transform: translateZ(0);
}

/* 进度条文字防闪烁 */
.progress-text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}
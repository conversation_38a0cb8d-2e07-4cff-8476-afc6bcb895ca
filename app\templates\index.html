{% extends "base.html" %}

{% block title %}有声图书生成器 - 首页{% endblock %}

{% block content %}
<!-- 首页内容 -->
<div id="home-content" class="page-content">
    <!-- Landing 页面 -->
    <div id="landing-section" class="landing-section">
        <!-- 英雄区域 -->
        <div class="hero-landing">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-magic"></i>
                    AI 有声图书生成器
                </h1>
                <p class="hero-subtitle">将您的图书文件快速转换为高品质音频，享受随时随地的听书体验</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">10,000+</span>
                        <span class="stat-label">已转换书籍</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">98%</span>
                        <span class="stat-label">转换成功率</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">3分钟</span>
                        <span class="stat-label">平均转换时间</span>
                    </div>
                </div>
                <button id="btn-start-conversion" class="md-button md-filled-button btn-large">
                    <i class="fas fa-rocket"></i>
                    立即开始转换
                </button>
            </div>
            <div class="hero-illustration">
                <div class="floating-elements">
                    <div class="book-icon"><i class="fas fa-book"></i></div>
                    <div class="arrow-icon"><i class="fas fa-arrow-right"></i></div>
                    <div class="audio-icon"><i class="fas fa-headphones"></i></div>
                </div>
            </div>
        </div>

        <!-- 使用说明区域 -->
        <div class="how-to-use-section">
            <div class="section-header">
                <h2><i class="fas fa-question-circle"></i> 如何使用</h2>
                <p>三个简单步骤，轻松转换您的图书为音频</p>
            </div>
            
            <div class="steps-container">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>上传文件</h3>
                        <p>支持 EPUB、PDF、TXT、Word 格式，或直接输入网页链接</p>
                        <div class="step-formats">
                            <span class="format-tag"><i class="fas fa-file-alt"></i> EPUB</span>
                            <span class="format-tag"><i class="fas fa-file-pdf"></i> PDF</span>
                            <span class="format-tag"><i class="fas fa-file-word"></i> Word</span>
                            <span class="format-tag"><i class="fas fa-link"></i> 网页</span>
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>AI 智能转换</h3>
                        <p>我们的 AI 系统将自动提取文本内容并生成高品质音频</p>
                        <div class="conversion-features">
                            <span class="feature-tag"><i class="fas fa-brain"></i> 智能分段</span>
                            <span class="feature-tag"><i class="fas fa-volume-up"></i> 自然语音</span>
                            <span class="feature-tag"><i class="fas fa-clock"></i> 快速处理</span>
                        </div>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>在线播放</h3>
                        <p>转换完成后，您可以在任何设备上在线收听或下载音频文件</p>
                        <div class="playback-features">
                            <span class="feature-tag"><i class="fas fa-mobile-alt"></i> 多设备</span>
                            <span class="feature-tag"><i class="fas fa-bookmark"></i> 书签功能</span>
                            <span class="feature-tag"><i class="fas fa-download"></i> 离线下载</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 特色功能区域 -->
        <div class="features-section">
            <div class="section-header">
                <h2><i class="fas fa-star"></i> 特色功能</h2>
                <p>为您提供最佳的听书体验</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>极速转换</h3>
                    <p>平均 3 分钟内完成转换，让您快速开始听书之旅</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>隐私保护</h3>
                    <p>文件加密存储，转换完成后可随时删除，保护您的隐私</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>多设备同步</h3>
                    <p>支持手机、平板、电脑等多设备，随时随地继续收听</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3>播放控制</h3>
                    <p>支持变速播放、进度跳转、书签标记等丰富功能</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3>多语言支持</h3>
                    <p>支持中文、英文等多种语言的文本识别和语音合成</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>高品质音频</h3>
                    <p>采用先进的语音合成技术，提供自然流畅的听觉体验</p>
                </div>
            </div>
        </div>

        <!-- 立即开始区域 -->
        <div class="cta-section">
            <div class="cta-content">
                <h2>准备好开始您的听书之旅吗？</h2>
                <p>立即注册并获得 100 积分，足够转换 2 本书籍</p>
                <div class="cta-buttons">
                    <button id="btn-cta-register" class="md-button md-filled-button btn-large">
                        <i class="fas fa-user-plus"></i>
                        免费注册
                    </button>
                    <button id="btn-cta-demo" class="md-button md-outlined-button btn-large">
                        <i class="fas fa-play"></i>
                        查看演示
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创作工坊页面 -->
<div id="workshop-content" class="page-content" style="display: none;">
    <div class="workshop-container">
        <div class="workshop-header">
            <h1><i class="fas fa-tools"></i> 创作工坊</h1>
            <p>上传您的图书文件，开始AI音频转换之旅</p>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-section">
            <div class="upload-area" id="upload-area">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <h3>拖拽文件到此处或点击上传</h3>
                    <p>支持 EPUB、PDF、TXT、Word 格式，最大 50MB</p>
                    <input type="file" id="file-input" accept=".epub,.pdf,.txt,.doc,.docx" style="display: none;">
                    <button id="upload-btn" class="md-button md-filled-button">
                        <i class="fas fa-plus"></i> 选择文件
                    </button>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="tasks-section">
            <h2><i class="fas fa-list"></i> 转换任务</h2>
            <div id="workshop-tasks-list" class="tasks-list">
                <div class="empty-state">
                    <i class="fas fa-tasks"></i>
                    <h3>暂无任务</h3>
                    <p>上传您的第一个图书文件开始转换吧！</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 我的任务页面 -->
<div id="my-tasks-content" class="page-content" style="display: none;">
    <div class="my-tasks-container">
        <div class="page-header">
            <h1><i class="fas fa-tasks"></i> 我的任务</h1>
            <p>查看和管理您的所有转换任务</p>
        </div>

        <!-- 任务过滤器 -->
        <div class="task-filters">
            <button class="filter-btn active" data-status="all">
                <i class="fas fa-list"></i>
                全部任务
            </button>
            <button class="filter-btn" data-status="pending">
                <i class="fas fa-clock"></i>
                处理中
            </button>
            <button class="filter-btn" data-status="completed">
                <i class="fas fa-check-circle"></i>
                已完成
            </button>
            <button class="filter-btn" data-status="failed">
                <i class="fas fa-exclamation-circle"></i>
                失败
            </button>
        </div>

        <div id="my-tasks-list" class="my-tasks-list">
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载任务...</p>
            </div>
        </div>

        <!-- 任务分页导航 -->
        <div id="tasks-pagination" class="library-pagination" style="display: none;">
            <button id="tasks-prev-page" class="pagination-btn" disabled>
                <i class="fas fa-chevron-left"></i>
                上一页
            </button>
            <div class="pagination-info">
                <span id="tasks-current-page">1</span> / <span id="tasks-total-pages">1</span>
            </div>
            <button id="tasks-next-page" class="pagination-btn" disabled>
                下一页
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- 音频库页面 -->
<div id="my-library-content" class="page-content" style="display: none;">
    <div class="library-container">
        <div class="page-header">
            <div class="header-left">
                <h1><i class="fas fa-book-open"></i> 音频库</h1>
                <p>您的专属音频图书收藏</p>
            </div>
            <div class="header-right">
                <div id="library-book-count" class="library-book-count-header">
                    <!-- 书籍数量将由JavaScript动态更新 -->
                </div>
            </div>
        </div>

        <!-- 标签式排序控制 -->
        <div class="library-sort-tabs">
            <button class="sort-tab active" data-sort="recent">
                <i class="fas fa-clock"></i>
                最近添加
            </button>
            <button class="sort-tab" data-sort="name">
                <i class="fas fa-sort-alpha-down"></i>
                按名称
            </button>
            <button class="sort-tab" data-sort="duration">
                <i class="fas fa-hourglass-half"></i>
                按时长
            </button>
        </div>

        <div id="audio-library-list" class="audio-library-list">
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载音频库...</p>
            </div>
        </div>

        <!-- 分页导航 -->
        <div id="library-pagination" class="library-pagination" style="display: none;">
            <button id="prev-page" class="pagination-btn" disabled>
                <i class="fas fa-chevron-left"></i>
                上一页
            </button>
            <div class="pagination-info">
                <span id="current-page">1</span> / <span id="total-pages">1</span>
            </div>
            <button id="next-page" class="pagination-btn" disabled>
                下一页
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- 积分页面 -->
<div id="my-points-content" class="page-content" style="display: none;">
    <div class="points-container">
        <div class="page-header">
            <h1><i class="fas fa-coins"></i> 我的积分</h1>
            <p>查看积分余额和使用记录</p>
        </div>

        <!-- 积分统计卡片 -->
        <div id="points-stats" class="points-stats">
            <!-- 统计卡片将由JavaScript动态生成 -->
        </div>

        <!-- 积分历史记录 -->
        <div class="points-section">
            <h2><i class="fas fa-history"></i> 积分记录</h2>
            <div id="points-history" class="points-history">
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在加载积分记录...</p>
            </div>
        </div>
    </div>
</div>

<!-- 音频播放器页面 -->
<div id="audio-player-page" class="page-content" style="display: none;">
    <!-- 播放器内容将由JavaScript动态生成 -->
</div>

<!-- 增强版音频播放器页面 -->
<div id="enhanced-audio-player-page" class="page-content" style="display: none;">
    <div id="enhanced-player-container">
        <!-- 增强版播放器内容将由JavaScript动态生成 -->
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 首页特定的JavaScript代码
document.addEventListener('DOMContentLoaded', function() {
    // 开始转换按钮点击事件
    document.getElementById('btn-start-conversion')?.addEventListener('click', function() {
        // 检查用户是否已登录
        const token = localStorage.getItem('access_token');
        if (!token) {
            // 显示登录模态框
            document.getElementById('auth-modal').style.display = 'block';
        } else {
            // 跳转到创作工坊
            window.location.href = '/workshop';
        }
    });

    // CTA注册按钮
    document.getElementById('btn-cta-register')?.addEventListener('click', function() {
        document.getElementById('auth-modal').style.display = 'block';
        document.getElementById('login-form').style.display = 'none';
        document.getElementById('register-form').style.display = 'block';
    });
});
</script>
{% endblock %}
